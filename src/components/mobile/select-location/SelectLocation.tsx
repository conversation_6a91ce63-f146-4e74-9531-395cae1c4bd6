import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import Modal from 'react-native-modal';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import {
	ActivityIndicator,
	Alert,
	I18nManager,
	InteractionManager,
	LayoutChangeEvent,
	Platform,
	StatusBar,
	StyleSheet,
	TextInput,
	TouchableOpacity,
	View
} from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Local components
import LocationInformation from './LocationInformation';
import ShippingNumber from './ShippingNumber';
import PlacesSearchResults from './PlacesSearchResults';

// Icons
import {
	ArrowLeft,
	CloseCircle,
	LocationGPS,
	LocationPin,
	Search
} from '../../../assets/svgs/icons';

// Constants and utilities
import {
	HORIZONTAL_DIMENS,
	SEARCH_TYPES,
	VERTICAL_DIMENS
} from '../../../constants';
import {
	getTenantCities,
	placesAutoComplete,
	addCity
} from '../../../redux/apis/common';
import { useAppSelector } from '../../../redux/hooks';
import { colors, fonts, headerHeightMobile } from '../../../utils/theme';
import { updateCustomerTenantShippingAddress } from '../../../redux/apis/customer';
import {
	Geocoder,
	checkBadRequest,
	getCurrentLocation
} from '../../../utils/functions';
import { getTenantCountry } from '../../../redux/selectors';
import useStatusBarHeight from '../../../hooks/useStatusbarHeight';

// Types
interface LocationData {
	[key: string]: string | number | undefined;
	latitude?: number;
	longitude?: number;
	city?: string;
	region?: string;
	country?: string;
	shippingAddress?: string;
	shippingCityId?: string;
	shippingRegionId?: string;
	shippingMobileNumber?: string;
	shippingCountryCode?: string;
}

interface AddressDetails {
	city: string;
	shippingCityId: string;
	region: string;
	shippingRegionId: string;
	country: string;
	shippingAddress: string;
	regionCode: string;
}

type Props = {
	isVisible: boolean;
	onClose: () => void;
	onSelect: (data: LocationData) => void;
	customerRoleId?: string;
	shippingNumber?: string | undefined;
	initialLocation?: {
		latitude: number;
		longitude: number;
	};
};

// Constants
const DEFAULT_REGION: Region = {
	latitude: 37.78825,
	longitude: -122.4324,
	latitudeDelta: 0.015,
	longitudeDelta: 0.0121
};

const INITIAL_ADDRESS_DETAILS: AddressDetails = {
	city: '',
	shippingCityId: '',
	region: '',
	shippingRegionId: '',
	country: '',
	shippingAddress: '',
	regionCode: ''
};

const SelectLocation = ({
	isVisible,
	onClose,
	onSelect,
	customerRoleId,
	shippingNumber,
	initialLocation
}: Props) => {
	// Hooks
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const { bottom } = useSafeAreaInsets();
	const statusBarHeight = useStatusBarHeight();

	// Refs
	const mapRef = useRef<MapView | null>(null);

	// Redux selectors
	const tenantCountry = useAppSelector(getTenantCountry);
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const countryId = currentRole?.tenant_id?.country?._id;
	const countryName = currentRole?.tenant_id?.country?.name;

	// State
	const [height, setHeight] = useState<number>(0);
	const [step, setStep] = useState<'1' | '2'>('1');
	const [searchKey, setSearchKey] = useState<string>('');
	const [savingLocation, setSavingLocation] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	const [locationLoading, setLocationLoading] = useState<boolean>(false);
	const [region, setRegion] = useState<Region>(
		initialLocation
			? { ...DEFAULT_REGION, ...initialLocation }
			: DEFAULT_REGION
	);
	const [addressDetails, setAddressDetails] = useState<AddressDetails>(INITIAL_ADDRESS_DETAILS);
	const [hasTriedInitialLocation, setHasTriedInitialLocation] = useState<boolean>(false);

	// Effects
	useEffect(() => {
		let isCancelled = false;

		if (region?.latitude && region?.longitude) {
			setLoading(true);
			Geocoder.from(region.latitude, region.longitude)
				.then((json: any) => {
					if (!isCancelled && json.results?.[0]) {
						const result = json.results[0];
						const address = Geocoder.getAddressComponents(result);
						setAddressDetails(address);
					}
				})
				.catch((error: any) => {
					if (!isCancelled) {
						console.warn('Geocoding error:', error);
					}
				})
				.finally(() => {
					if (!isCancelled) {
						setLoading(false);
					}
				});
		}

		return () => {
			isCancelled = true;
		};
	}, [region]);

	useEffect(() => {
		if (searchKey.trim()) {
			dispatch(placesAutoComplete(searchKey));
		}
	}, [searchKey, dispatch]);

	// Reset state when modal closes
	useEffect(() => {
		if (!isVisible) {
			setStep('1');
			setSearchKey('');
			setLoading(false);
			setSavingLocation(false);
			setLocationLoading(false);
			setAddressDetails(INITIAL_ADDRESS_DETAILS);
			setHasTriedInitialLocation(false);
			// Reset region to initial location or default
			setRegion(initialLocation
				? { ...DEFAULT_REGION, ...initialLocation }
				: DEFAULT_REGION
			);
		}
	}, [isVisible, initialLocation]);

	// Location functions
	const getUserLocation = useCallback(async (showLoading = true) => {
		if (hasTriedInitialLocation && !initialLocation) {
			return; // Don't try again if we already tried and there's no initial location
		}

		try {
			if (showLoading) {
				setLocationLoading(true);
			}

			const location: any = await getCurrentLocation();
			if (location?.coords) {
				const { coords } = location;
				const newRegion = {
					latitude: coords.latitude,
					longitude: coords.longitude,
					latitudeDelta: 0.015,
					longitudeDelta: 0.0121
				};

				setRegion(newRegion);
				// Use setTimeout to ensure map is ready
				setTimeout(() => {
					mapRef.current?.animateToRegion(newRegion, 1000);
				}, 500);

				setHasTriedInitialLocation(true);
			}
		} catch (error) {
			console.warn('Failed to get user location:', error);
			setHasTriedInitialLocation(true);
		} finally {
			if (showLoading) {
				setLocationLoading(false);
			}
		}
	}, [hasTriedInitialLocation, initialLocation]);

	// Auto-fetch location when modal opens
	useEffect(() => {
		if (isVisible && !hasTriedInitialLocation && !initialLocation) {
			// Delay to ensure modal animation is complete and map is ready
			const timer = setTimeout(() => {
				getUserLocation(true);
			}, 800);

			return () => clearTimeout(timer);
		}
	}, [isVisible, hasTriedInitialLocation, initialLocation, getUserLocation]);

	// Event handlers
	const handleContentLayout = useCallback((event: LayoutChangeEvent) => {
		if (step === '1') {
			setHeight(event.nativeEvent.layout.height + 24);
		}
	}, [step]);

	const handleRegionChange = useCallback((newRegion: Region) => {
		setRegion(newRegion);
	}, []);

	const handleGoBack = useCallback(() => {
		if (step === '2') {
			setStep('1');
		} else {
			onClose();
		}
	}, [step, onClose]);

	const handleUserLocationPress = useCallback(async () => {
		try {
			const location: any = await getCurrentLocation();
			if (location?.coords) {
				const { coords } = location;
				mapRef.current?.animateToRegion({
					latitude: coords.latitude,
					longitude: coords.longitude,
					latitudeDelta: 0.015,
					longitudeDelta: 0.0121
				}, 1000);
			}
		} catch (error) {
			console.warn('Failed to get user location:', error);
		}
	}, []);

	const getCities = () => {
		const selectedCity = addressDetails.city.toLowerCase();
		return new Promise<Array<any>>(async (resolve) => {
			try {
				const params = {
					type: SEARCH_TYPES.SEARCH,
					searchKey: selectedCity
				};
				const response = await dispatch(getTenantCities(params));
				if (!response.error) {
					resolve(response.payload.data);
					return;
				}
				resolve([]);
			} catch {
				resolve([]);
			}
		});
	};

	const onNext = async () => {
		const selectedCity = addressDetails.city.toLowerCase();
		setLoading(true);
		const cities = await getCities();
		const matchingCity = cities.find((x) => {
			const englishMactch = selectedCity === x.name.toLowerCase();
			const secondaryMatch =
				selectedCity === x.secondary_language_name?.toLowerCase();
			return englishMactch || secondaryMatch;
		});
		if (matchingCity) {
			if (countryId && matchingCity?.country_id?._id === countryId) {
				setLoading(false);
				setAddressDetails({
					...addressDetails,
					shippingCityId: matchingCity._id,
					shippingRegionId: matchingCity.region_id?._id
				});
				setStep('2');
			} else {
				setLoading(false);
				Alert.alert(`${t('select_location_within')} ${countryName}.`);
			}
		} else {
			const requestBody: any = {
				cityName: addressDetails.city,
				countryName: addressDetails.country,
				regionName: addressDetails.region
			};
			if (addressDetails.regionCode) {
				requestBody.regionCode = addressDetails.regionCode;
			}
			try {
				const response = await dispatch(addCity(requestBody));
				setLoading(false);
				if (response.error) {
					// console.log("🚀 ~ onNext ~ response.error:", JSON.stringify(response.error))
					Alert.alert(t(response.payload.message));
				} else {
					dispatch(getTenantCities()); // Update cities list
					setAddressDetails({
						...addressDetails,
						shippingCityId: response.payload.data._id,
						shippingRegionId: response.payload.data.region_id
					});
					setStep('2');
				}
			} catch (error) {
				Alert.alert('AddCityError', `${JSON.stringify(error)}`);
			}
		}
	};

	/* Pass selected location, address details and mobile number to add customer screen */
	const onSubmitNumber = async (
		shippingMobileNumber: string,
		shippingCountryCode: string
	) => {
		//If leading zero then remove
		let number = shippingMobileNumber;
		const result = number.startsWith('0');
		result === true ? (number = number.substring(1)) : number;

		if (customerRoleId) {
			// Customer role id available so save location in database then close modal
			setSavingLocation(true);
			const requestBody = {
				ids: [customerRoleId],
				updateFields: {
					shipping_address: addressDetails.shippingAddress,
					shipping_country_id: tenantCountry._id,
					shipping_city_id: addressDetails.shippingCityId,
					shipping_region_id: addressDetails.shippingRegionId,
					shipping_country_code: shippingCountryCode,
					shipping_mobile_number: number,
					gps_coordinates: {
						latitude: region?.latitude,
						longitude: region?.longitude
					}
				}
			};
			const response = await dispatch(
				updateCustomerTenantShippingAddress(requestBody)
			);
			setSavingLocation(false);
			if (!response.error) {
				onSelect({
					...addressDetails,
					...region,
					shippingMobileNumber,
					shippingCountryCode
				});
				InteractionManager.runAfterInteractions(() => {
					setStep('1'); // Start from step one if user open select location again
				});
			} else {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
			return;
		}

		// if (customerRoleId && userType === USER_TYPES.CUSTOMER_APP) {
		// 	// Customer role id available so save location in database then close modal
		// 	setSavingLocation(true);
		// 	const requestBody = {
		// 		shipping_address: addressDetails.shippingAddress,
		// 		shipping_country_id: tenantCountry._id,
		// 		shipping_city_id: addressDetails.shippingCityId,
		// 		shipping_region_id: addressDetails.shippingRegionId,
		// 		shipping_country_code: shippingCountryCode,
		// 		shipping_mobile_number: number,
		// 		gps_coordinates: {
		// 			latitude: region?.latitude,
		// 			longitude: region?.longitude
		// 		}
		// 	};
		// 	const response = await dispatch(updateCustomerShippingAddress(requestBody));
		// 	setSavingLocation(false);
		// 	if (!response.error) {
		// 		onSelect({
		// 			...addressDetails,
		// 			...region,
		// 			shippingMobileNumber,
		// 			shippingCountryCode
		// 		});
		// 		InteractionManager.runAfterInteractions(() => {
		// 			setStep('1'); // Start from step one if user open select location again
		// 		});
		// 	} else {
		// 		Alert.alert(t(checkBadRequest(response.payload)));
		// 	}
		// 	return;
		// }

		onSelect({
			...addressDetails,
			...region,
			shippingMobileNumber,
			shippingCountryCode
		});
		InteractionManager.runAfterInteractions(() => {
			setStep('1'); // Start from step one if user open select location again
		});
	};



	const handlePlaceSelect = useCallback((location: any) => {
		setSearchKey('');
		if (location?.lat && location?.lng) {
			mapRef.current?.animateToRegion({
				latitude: location.lat,
				longitude: location.lng,
				latitudeDelta: 0.015,
				longitudeDelta: 0.0121
			}, 1000);
		}
	}, []);

	return (
		<Modal
			isVisible={isVisible}
			style={styles.modalContainer}
		>
			<View style={styles.modalContent}>
				<StatusBar
					barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
				/>
				<MapView
					provider={PROVIDER_GOOGLE}
					ref={mapRef}
					style={styles.map}
					initialRegion={region}
					rotateEnabled={false}
					pitchEnabled={false}
					showsUserLocation={true}
					showsMyLocationButton={false}
					showsIndoors={false}
					onRegionChangeComplete={handleRegionChange}
					onRegionChangeStart={() => {
						setLoading(true);
					}}
					mapPadding={{ top: 0, right: 0, bottom: height - 22, left: 0 }}
					scrollEnabled={step === '1'}
					zoomControlEnabled={step === '1'}
					zoomEnabled={step === '1'}
					zoomTapEnabled={step === '1'}
					scrollDuringRotateOrZoomEnabled={step === '1'}
				/>
				<View
					style={[styles.markerFixed, { bottom: height }]}
					pointerEvents="none"
				>
					<LocationPin fill={colors.primary} />
				</View>
				{step === '1' && (
					<TouchableOpacity
						style={[styles.locationBtn, { bottom: height + 16 }]}
						onPress={handleUserLocationPress}
						disabled={locationLoading}
					>
						{locationLoading ? (
							<ActivityIndicator color={colors.primary} size="small" />
						) : (
							<LocationGPS fill={colors.primary} />
						)}
					</TouchableOpacity>
				)}
				<View
					style={[
						styles.modalHeader,
						Platform.OS === 'ios' && { top: statusBarHeight }
					]}
				>
					<TouchableOpacity onPress={handleGoBack} disabled={savingLocation}>
						<ArrowLeft
							fill={colors.primary}
							style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }}
						/>
					</TouchableOpacity>
					<View style={styles.searchInputContainer}>
						<TextInput
							style={styles.searchInput}
							placeholder={t('search_place')}
							placeholderTextColor={colors.grey400}
							value={searchKey}
							onChangeText={setSearchKey}
							editable={step === '1'}
						// onFocus={() => setShowPlaces(true)}
						// onBlur={() => setShowPlaces(false)}
						/>
						<TouchableOpacity
							style={styles.clearSearchBtn}
							onPress={() => setSearchKey('')}
							disabled={!searchKey}
						>
							<CloseCircle fill={colors.grey600} />
						</TouchableOpacity>
						<View style={styles.searchIconContainer}>
							<Search fill={colors.grey600} />
						</View>
						<PlacesSearchResults onSelect={handlePlaceSelect} />
					</View>
				</View>
				<BottomSheet
					index={0}
					animateOnMount={true}
					handleIndicatorStyle={styles.bottomSheetIndicator}
					keyboardBlurBehavior="restore"
					enableDynamicSizing={true}
				>
					<BottomSheetView
						onLayout={handleContentLayout}
						style={[
							styles.bottomSheet,
							bottom > 0 && { paddingBottom: bottom },
							Platform.OS === 'android' && {
								paddingBottom: VERTICAL_DIMENS._20
							}
						]}
					>
						{step === '1' && (
							<LocationInformation
								addressDetails={addressDetails}
								onSubmit={onNext}
								loading={loading}
							/>
						)}
						{step === '2' && (
							<ShippingNumber
								loading={savingLocation}
								onSubmit={onSubmitNumber}
								shippingNumber={shippingNumber}
							/>
						)}
					</BottomSheetView>
				</BottomSheet>
			</View>
		</Modal>
	);
};

const styles = StyleSheet.create({
	modalContainer: {
		justifyContent: 'flex-end',
		margin: 0
	},
	modalContent: {
		backgroundColor: colors.grey100,
		flex: 1
	},
	modalHeader: {
		alignItems: 'center',
		paddingHorizontal: HORIZONTAL_DIMENS._16,
		flexDirection: 'row',
		width: '100%',
		height: headerHeightMobile,
		position: 'absolute'
	},
	searchInputContainer: {
		flex: 1,
		marginLeft: HORIZONTAL_DIMENS._14
	},
	searchInput: {
		backgroundColor: colors.white,
		borderRadius: 20,
		color: colors.primary,
		fontFamily: fonts.Montserrat.Regular,
		fontWeight: '400',
		fontSize: HORIZONTAL_DIMENS._14,
		height: 40,
		justifyContent: 'center',
		paddingLeft: HORIZONTAL_DIMENS._48,
		textAlign: I18nManager.isRTL ? 'right' : 'left',
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	searchIconContainer: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		left: HORIZONTAL_DIMENS._16
	},
	clearSearchBtn: {
		position: 'absolute',
		height: 40,
		justifyContent: 'center',
		paddingRight: HORIZONTAL_DIMENS._10,
		right: 0
	},
	map: {
		flex: 1
	},
	markerFixed: {
		position: 'absolute',
		top: 0,
		bottom: 22,
		left: 0,
		right: 0,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: 'transparent'
	},
	bottomSheet: {
		paddingHorizontal: HORIZONTAL_DIMENS._16
	},
	bottomSheetIndicator: {
		backgroundColor: colors.grey300
	},
	locationBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 30,
		justifyContent: 'center',
		position: 'absolute',
		//bottom: '42%',
		right: 16,
		height: 54,
		width: 54,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.14,
		shadowRadius: 2.62,
		elevation: 3
	}
});

export { SelectLocation };
